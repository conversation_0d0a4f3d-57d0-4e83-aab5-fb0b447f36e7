<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "286B0522-**************-164D7E591B00"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E79EE897-51AB-4F31-9399-C20FA7D1188B"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "CarbonCoin/Services/ItemCard/ItemCardManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "310"
            endingLineNumber = "310"
            landmarkName = "createItemCard(title:description:tags:imageFileName:imageURL:authorId:location:latitude:longitude:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
