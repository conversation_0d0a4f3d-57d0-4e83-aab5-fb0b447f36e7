//
//  ContentView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct ContentView: View {
    @EnvironmentObject private var healthManager: HealthManager
    @EnvironmentObject private var locationViewModel: LocationViewModel
    @StateObject private var healthDataViewModel : HealthDataViewModel
    @StateObject private var authManager = AuthManager()
    @AppStorage("isLoggedIn") private var isLoggedIn = false

    init(){
        // 使用未初始化的 healthManager 会报错，所以需要使用 `_healthDataViewModel`
        let healthManager = HealthManager() // 或其他方式获取
        self._healthDataViewModel = StateObject(wrappedValue: HealthDataViewModel(dataType: .steps, healthManager: healthManager))
    }

    var body: some View {
        Group {
            if isLoggedIn {
                // 已登录，显示主界面
                MainTabView()
                    .environmentObject(healthDataViewModel)
                    .environmentObject(authManager)
                    .environmentObject(locationViewModel)
            } else {
                // 未登录，显示登录界面
                LoginView()
                    .environmentObject(authManager)
            }
        }
        .onAppear {
            // 检查登录状态
            checkAuthState()
        }
        .onChange(of: authManager.isLoggedIn) { _, newValue in
            isLoggedIn = newValue

            // 用户登录状态变化时处理位置更新
            if newValue && !authManager.currentUserId.isEmpty {
                // 用户登录，开始自动位置更新
                locationViewModel.startAutoLocationUpdates(for: authManager.currentUserId)
                print("📍 用户登录，开始自动位置更新: \(authManager.currentUserId)")
            } else {
                // 用户登出，停止自动位置更新
                locationViewModel.stopAutoLocationUpdates()
                print("📍 用户登出，停止自动位置更新")
            }
        }
    }

    // MARK: - Private Methods

    /// 检查认证状态
    private func checkAuthState() {
        // 同步AuthManager的登录状态
        isLoggedIn = authManager.isLoggedIn

        // 如果已登录，开始位置更新
        if isLoggedIn && !authManager.currentUserId.isEmpty {
            locationViewModel.startAutoLocationUpdates(for: authManager.currentUserId)
            print("📍 应用启动时检测到已登录，开始自动位置更新: \(authManager.currentUserId)")
        }
    }
}

#Preview {
    ContentView()
}
