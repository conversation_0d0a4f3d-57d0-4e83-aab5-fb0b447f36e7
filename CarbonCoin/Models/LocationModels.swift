//
//  LocationModels.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/30.
//

import Foundation
import CoreLocation

// MARK: - 用户位置信息模型

/// 用户位置信息
struct UserLocationInfo: Codable, Identifiable, Equatable {
    let id = UUID()
    let userId: String
    let latitude: Double
    let longitude: Double
    let timestamp: Date
    let accuracy: Double?
    let locationString: String?
    
    enum CodingKeys: String, CodingKey {
        case userId, latitude, longitude, timestamp, accuracy, locationString
    }
    
    /// 从CLLocation创建位置信息
    init(userId: String, location: CLLocation, locationString: String? = nil) {
        self.userId = userId
        self.latitude = location.coordinate.latitude
        self.longitude = location.coordinate.longitude
        self.timestamp = location.timestamp
        self.accuracy = location.horizontalAccuracy
        self.locationString = locationString
    }
    
    /// 手动创建位置信息
    init(userId: String, latitude: Double, longitude: Double, timestamp: Date = Date(), accuracy: Double? = nil, locationString: String? = nil) {
        self.userId = userId
        self.latitude = latitude
        self.longitude = longitude
        self.timestamp = timestamp
        self.accuracy = accuracy
        self.locationString = locationString
    }
    
    /// 转换为CLLocation
    var clLocation: CLLocation {
        return CLLocation(
            coordinate: CLLocationCoordinate2D(latitude: latitude, longitude: longitude),
            altitude: 0,
            horizontalAccuracy: accuracy ?? 0,
            verticalAccuracy: 0,
            timestamp: timestamp
        )
    }
    
    /// 格式化的时间戳
    var formattedTimestamp: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: timestamp)
    }
}

// MARK: - API请求模型

/// 更新用户位置请求
struct UpdateLocationRequest: Codable {
    let userId: String
    let latitude: Double
    let longitude: Double

    init(userId: String, location: CLLocation) {
        self.userId = userId
        self.latitude = location.coordinate.latitude
        self.longitude = location.coordinate.longitude
    }

    init(userLocationInfo: UserLocationInfo) {
        self.userId = userLocationInfo.userId
        self.latitude = userLocationInfo.latitude
        self.longitude = userLocationInfo.longitude
    }
}

// MARK: - API响应模型

/// 位置查询响应
struct LocationQueryResponse: Codable {
    let success: Bool
    let data: LocationData?
    let error: String?
}

/// 位置更新响应
struct LocationUpdateResponse: Codable {
    let success: Bool
    let message: String?
}

/// 位置数据（匹配后端API返回格式）
struct LocationData: Codable {
    let userId: String
    let latitude: Double
    let longitude: Double

    /// 转换为UserLocationInfo
    func toUserLocationInfo() -> UserLocationInfo {
        return UserLocationInfo(
            userId: userId,
            latitude: latitude,
            longitude: longitude,
            timestamp: Date(),
            accuracy: nil,
            locationString: nil
        )
    }
}

// MARK: - 位置历史记录模型

/// 位置历史记录（本地存储）
struct LocationHistoryItem: Codable, Identifiable, Equatable {
    let id = UUID()
    let userLocationInfo: UserLocationInfo
    let createdAt: Date
    
    enum CodingKeys: String, CodingKey {
        case userLocationInfo, createdAt
    }
    
    init(userLocationInfo: UserLocationInfo, createdAt: Date = Date()) {
        self.userLocationInfo = userLocationInfo
        self.createdAt = createdAt
    }
    
    /// 格式化的创建时间
    var formattedCreatedAt: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: createdAt, relativeTo: Date())
    }
}

// MARK: - 位置错误类型

/// 位置服务错误枚举
enum LocationError: Error, LocalizedError {
    case permissionDenied
    case locationUnavailable
    case networkError
    case serverError
    case invalidResponse
    case userNotFound
    case invalidLocation
    case missingUserId
    case missingRequiredFields
    case unknown(String)

    var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return "位置权限被拒绝，请在设置中开启位置权限"
        case .locationUnavailable:
            return "无法获取位置信息"
        case .networkError:
            return "网络连接失败，请检查网络设置"
        case .serverError:
            return "服务器内部错误"
        case .invalidResponse:
            return "服务器响应格式错误"
        case .userNotFound:
            return "用户位置不存在"
        case .invalidLocation:
            return "位置信息无效"
        case .missingUserId:
            return "缺少 userId 参数"
        case .missingRequiredFields:
            return "缺少必需字段：userId、latitude 或 longitude"
        case .unknown(let message):
            return message
        }
    }
}

// MARK: - 位置更新配置

/// 位置更新配置
struct LocationUpdateConfig {
    /// 更新间隔（秒）
    static let updateInterval: TimeInterval = 10.0

    /// 最大历史记录数量
    static let maxHistoryCount: Int = 10

    /// 位置精度阈值（米）
    static let accuracyThreshold: Double = 100.0

    /// 最小移动距离（米）
    static let minimumDistance: Double = 2.0

    /// 请求超时时间（秒）
    static let requestTimeout: TimeInterval = 30.0
}
