//
//  PropEvent.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import Foundation
import CloudKit

/// 道具类型枚举
enum PropType: String, CaseIterable, Codable {
    case briefAnimation = "brief_animation"     // 短暂动画效果
    case persistentEffect = "persistent_effect" // 持续效果
    case textDisplay = "text_display"          // 文字显示
    
    /// 道具显示名称
    var displayName: String {
        switch self {
        case .briefAnimation:
            return "动画特效"
        case .persistentEffect:
            return "持续效果"
        case .textDisplay:
            return "文字消息"
        }
    }
    
    /// 道具持续时间（秒）
    var duration: TimeInterval {
        switch self {
        case .briefAnimation:
            return 3.0
        case .persistentEffect:
            return 30.0
        case .textDisplay:
            return 10.0
        }
    }
    
    /// 道具消耗的碳币
    var cost: Int {
        switch self {
        case .briefAnimation:
            return 5
        case .persistentEffect:
            return 15
        case .textDisplay:
            return 3
        }
    }
}

/// 道具事件模型
struct PropEvent: Codable, Identifiable {
    let id: UUID
    let fromUserId: UUID
    let toUserId: UUID
    let type: PropType
    let customData: [String: String]? // 扩展数据，如文字内容、动画参数等
    let createdAt: Date
    var isProcessed: Bool // 是否已处理
    
    init(id: UUID = UUID(),
         fromUserId: UUID,
         toUserId: UUID,
         type: PropType,
         customData: [String: String]? = nil) {
        self.id = id
        self.fromUserId = fromUserId
        self.toUserId = toUserId
        self.type = type
        self.customData = customData
        self.createdAt = Date()
        self.isProcessed = false
    }
    
    /// 获取文字内容（用于textDisplay类型）
    var textContent: String? {
        return customData?["text"]
    }
    
    /// 获取动画参数（用于briefAnimation类型）
    var animationParams: [String: String]? {
        return customData
    }
    
    /// 标记为已处理
    mutating func markAsProcessed() {
        isProcessed = true
    }
}

// MARK: - CloudKit Support
extension PropEvent {
    /// CloudKit记录类型名称
    static let recordType = "PropEvent"
    
    /// 从CloudKit记录创建道具事件
    init?(from record: CKRecord) {
        guard let fromUserIdString = record["fromUserId"] as? String,
              let fromUserId = UUID(uuidString: fromUserIdString),
              let toUserIdString = record["toUserId"] as? String,
              let toUserId = UUID(uuidString: toUserIdString),
              let typeString = record["type"] as? String,
              let type = PropType(rawValue: typeString),
              let createdAt = record["createdAt"] as? Date,
              let isProcessed = record["isProcessed"] as? Bool else {
            return nil
        }
        
        self.id = UUID(uuidString: record.recordID.recordName) ?? UUID()
        self.fromUserId = fromUserId
        self.toUserId = toUserId
        self.type = type
        self.createdAt = createdAt
        self.isProcessed = isProcessed
        
        // 处理自定义数据
        if let customDataString = record["customData"] as? String,
           let customDataData = customDataString.data(using: .utf8),
           let customData = try? JSONDecoder().decode([String: String].self, from: customDataData) {
            self.customData = customData
        } else {
            self.customData = nil
        }
    }
    
    /// 转换为CloudKit记录
    func toRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: id.uuidString)
        let record = CKRecord(recordType: PropEvent.recordType, recordID: recordID)
        
        record["fromUserId"] = fromUserId.uuidString
        record["toUserId"] = toUserId.uuidString
        record["type"] = type.rawValue
        record["createdAt"] = createdAt
        record["isProcessed"] = isProcessed
        
        // 处理自定义数据
        if let customData = customData,
           let customDataData = try? JSONEncoder().encode(customData),
           let customDataString = String(data: customDataData, encoding: .utf8) {
            record["customData"] = customDataString
        }
        
        return record
    }
}
