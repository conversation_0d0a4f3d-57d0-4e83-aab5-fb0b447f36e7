//
//  Track.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import Foundation
import CoreLocation
import CloudKit

/// 轨迹模型
struct Track: Codable, Identifiable {
    let id: UUID
    var name: String
    var points: [TrackPoint]
    let startTime: Date
    var endTime: Date?
    let userId: UUID
    var isCompleted: Bool
    var totalDistance: Double // 总距离（米）
    var carbonSaved: Double // 节省的碳排放（克）
    
    init(id: UUID = UUID(),
         name: String = "",
         points: [TrackPoint] = [],
         userId: UUID,
         isCompleted: Bool = false) {
        self.id = id
        self.name = name.isEmpty ? "轨迹 \(DateFormatter.shortDate.string(from: Date()))" : name
        self.points = points
        self.startTime = Date()
        self.endTime = nil
        self.userId = userId
        self.isCompleted = isCompleted
        self.totalDistance = 0
        self.carbonSaved = 0
        
        // 计算总距离和碳排放
        calculateMetrics()
    }
    
    /// 添加轨迹点
    mutating func addPoint(_ point: TrackPoint) {
        points.append(point)
        calculateMetrics()
    }
    
    /// 完成轨迹记录
    mutating func complete() {
        isCompleted = true
        endTime = Date()
        calculateMetrics()
    }
    
    /// 计算轨迹指标
    private mutating func calculateMetrics() {
        guard points.count > 1 else {
            totalDistance = 0
            carbonSaved = 0
            return
        }
        
        // 计算总距离
        var distance: Double = 0
        for i in 1..<points.count {
            distance += points[i-1].distance(to: points[i])
        }
        totalDistance = distance
        
        // 计算节省的碳排放（假设每公里节省120克碳排放）
        carbonSaved = (totalDistance / 1000) * 120
    }
    
    /// 获取轨迹持续时间
    var duration: TimeInterval {
        guard let endTime = endTime else {
            return Date().timeIntervalSince(startTime)
        }
        return endTime.timeIntervalSince(startTime)
    }
    
    /// 获取平均速度（米/秒）
    var averageSpeed: Double {
        guard duration > 0 else { return 0 }
        return totalDistance / duration
    }
}

// MARK: - CloudKit Support
extension Track {
    /// CloudKit记录类型名称
    static let recordType = "Track"
    
    /// 从CloudKit记录创建轨迹
    init?(from record: CKRecord) {
        guard let name = record["name"] as? String,
              let startTime = record["startTime"] as? Date,
              let userIdString = record["userId"] as? String,
              let userId = UUID(uuidString: userIdString),
              let isCompleted = record["isCompleted"] as? Bool,
              let totalDistance = record["totalDistance"] as? Double,
              let carbonSaved = record["carbonSaved"] as? Double else {
            return nil
        }
        
        self.id = UUID(uuidString: record.recordID.recordName) ?? UUID()
        self.name = name
        self.startTime = startTime
        self.endTime = record["endTime"] as? Date
        self.userId = userId
        self.isCompleted = isCompleted
        self.totalDistance = totalDistance
        self.carbonSaved = carbonSaved
        
        // 处理轨迹点数据
        if let pointsData = record["points"] as? Data,
           let points = try? JSONDecoder().decode([TrackPoint].self, from: pointsData) {
            self.points = points
        } else {
            self.points = []
        }
    }
    
    /// 转换为CloudKit记录
    func toRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: id.uuidString)
        let record = CKRecord(recordType: Track.recordType, recordID: recordID)
        
        record["name"] = name
        record["startTime"] = startTime
        record["endTime"] = endTime
        record["userId"] = userId.uuidString
        record["isCompleted"] = isCompleted
        record["totalDistance"] = totalDistance
        record["carbonSaved"] = carbonSaved
        
        // 处理轨迹点数据
        if let pointsData = try? JSONEncoder().encode(points) {
            record["points"] = pointsData
        }
        
        return record
    }
}

// MARK: - Helper Extensions
extension DateFormatter {
    static let shortDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .none
        return formatter
    }()
}
