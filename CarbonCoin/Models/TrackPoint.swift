//
//  TrackPoint.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import Foundation
import CoreLocation
import CloudKit

/// 轨迹点模型
struct TrackPoint: Codable, Identifiable {
    let id: UUID
    let latitude: Double
    let longitude: Double
    let altitude: Double
    let timestamp: Date
    let accuracy: Double
    let speed: Double
    
    init(id: UUID = UUID(), 
         location: CLLocation) {
        self.id = id
        self.latitude = location.coordinate.latitude
        self.longitude = location.coordinate.longitude
        self.altitude = location.altitude
        self.timestamp = location.timestamp
        self.accuracy = location.horizontalAccuracy
        self.speed = location.speed
    }
    
    init(id: UUID = UUID(),
         latitude: Double,
         longitude: Double,
         altitude: Double = 0,
         timestamp: Date = Date(),
         accuracy: Double = 5.0,
         speed: Double = 0) {
        self.id = id
        self.latitude = latitude
        self.longitude = longitude
        self.altitude = altitude
        self.timestamp = timestamp
        self.accuracy = accuracy
        self.speed = speed
    }
    
    /// 转换为CLLocation
    var location: CLLocation {
        return CLLocation(
            coordinate: CLLocationCoordinate2D(latitude: latitude, longitude: longitude),
            altitude: altitude,
            horizontalAccuracy: accuracy,
            verticalAccuracy: accuracy,
            course: 0,
            speed: speed,
            timestamp: timestamp
        )
    }
    
    /// 计算与另一个点的距离（米）
    func distance(to other: TrackPoint) -> Double {
        return location.distance(from: other.location)
    }
}

// MARK: - CloudKit Support
extension TrackPoint {
    /// CloudKit记录类型名称
    static let recordType = "TrackPoint"
    
    /// 从CloudKit记录创建轨迹点
    init?(from record: CKRecord) {
        guard let latitude = record["latitude"] as? Double,
              let longitude = record["longitude"] as? Double,
              let altitude = record["altitude"] as? Double,
              let timestamp = record["timestamp"] as? Date,
              let accuracy = record["accuracy"] as? Double,
              let speed = record["speed"] as? Double else {
            return nil
        }
        
        self.id = UUID(uuidString: record.recordID.recordName) ?? UUID()
        self.latitude = latitude
        self.longitude = longitude
        self.altitude = altitude
        self.timestamp = timestamp
        self.accuracy = accuracy
        self.speed = speed
    }
    
    /// 转换为CloudKit记录
    func toRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: id.uuidString)
        let record = CKRecord(recordType: TrackPoint.recordType, recordID: recordID)
        
        record["latitude"] = latitude
        record["longitude"] = longitude
        record["altitude"] = altitude
        record["timestamp"] = timestamp
        record["accuracy"] = accuracy
        record["speed"] = speed
        
        return record
    }
}
