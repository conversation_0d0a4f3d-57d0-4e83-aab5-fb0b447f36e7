//
//  ItemCardManager.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/30.
//

import Foundation
import CoreLocation

// MARK: - Error Types
public enum ItemCardError: LocalizedError {
    case invalidURL
    case noData
    case decodingError(Error)
    case networkError(Error)
    case serverError(String)
    case missingParameters

    public var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .noData:
            return "没有数据"
        case .decodingError(let error):
            return "数据解析错误: \(error.localizedDescription)"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .serverError(let message):
            return "服务器错误: \(message)"
        case .missingParameters:
            return "缺少必要参数"
        }
    }
}

// MARK: - ItemCard 和 UserItemCard 统一管理服务
class ItemCardManager {

    // MARK: - Properties
    private let baseURL = AuthConfig.baseURL

    // MARK: - Response Models
    private struct APIResponse<T: Codable>: Codable {
        let success: Bool
        let data: T?
        let message: String?
        let error: String?
    }

    private struct UserItemCardsResponse: Codable {
        let cards: [UserItemCardAPI]
        let total: Int
    }

    // API 返回的 UserItemCard 结构（匹配后端）
    private struct UserItemCardAPI: Codable {
        let id: String
        let title: String
        let description: String
        let tags: [String]
        let imageFileName: String
        let imageURL: String
        let location: String
        let latitude: Double?
        let longitude: Double?
        let createdAt: String
        let author: AuthorInfo
        let remark: String?
        let acquiredAt: String
        let isOwner: Bool
        let userItemCardId: String

        struct AuthorInfo: Codable {
            let userId: String
            let nickname: String
            let avatarURL: String?
        }
    }

    // MARK: - Initialization
    init() {}

    // MARK: - UserItemCard API Methods

    /// 获取用户持有的所有卡片
    /// - Parameter userId: 用户ID
    /// - Returns: 用户持有的卡片列表
    func getUserItemCards(userId: String) async throws -> [UserItemCard] {
        guard !userId.isEmpty else {
            throw ItemCardError.missingParameters
        }

        guard let url = URL(string: "\(baseURL)/userItemCard?userId=\(userId)") else {
            throw ItemCardError.invalidURL
        }

        do {
            let (data, response) = try await URLSession.shared.data(from: url)

            // 检查HTTP状态码
            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode != 200 {
                throw ItemCardError.serverError("HTTP \(httpResponse.statusCode)")
            }

            let apiResponse = try JSONDecoder().decode(APIResponse<UserItemCardsResponse>.self, from: data)

            if apiResponse.success, let responseData = apiResponse.data {
                // 转换API模型到本地模型
                let userItemCards = responseData.cards.map { apiCard in
                    let itemCard = ItemCard(
                        id: apiCard.id,
                        tags: apiCard.tags,
                        description: apiCard.description,
                        title: apiCard.title,
                        imageFileName: apiCard.imageFileName,
                        imageURL: apiCard.imageURL,
                        createdAt: apiCard.createdAt.toDate() ?? Date(),
                        authorId: apiCard.author.userId,
                        location: apiCard.location,
                        latitude: apiCard.latitude,
                        longitude: apiCard.longitude
                    )

                    let authorInfo = AuthorInfo(
                        userId: apiCard.author.userId,
                        nickname: apiCard.author.nickname,
                        avatarURL: apiCard.author.avatarURL
                    )

                    return UserItemCard(
                        id: apiCard.userItemCardId,
                        userId: userId,
                        cardId: apiCard.id,
                        remark: apiCard.remark,
                        acquiredAt: apiCard.acquiredAt.toDate() ?? Date(),
                        isOwner: apiCard.isOwner,
                        card: itemCard,
                        author: authorInfo
                    )
                }

                return userItemCards
            } else {
                throw ItemCardError.serverError(apiResponse.error ?? "未知错误")
            }

        } catch let error as ItemCardError {
            throw error
        } catch {
            if error is DecodingError {
                throw ItemCardError.decodingError(error)
            } else {
                throw ItemCardError.networkError(error)
            }
        }
    }

    /// 更新用户卡片备注
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - cardId: 卡片ID
    ///   - remark: 新的备注内容
    /// - Returns: 更新后的用户卡片
    func updateUserItemCardRemark(userId: String, cardId: String, remark: String) async throws -> UserItemCard {
        guard !userId.isEmpty, !cardId.isEmpty else {
            throw ItemCardError.missingParameters
        }

        guard let url = URL(string: "\(baseURL)/userItemCard?userId=\(userId)&cardId=\(cardId)") else {
            throw ItemCardError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "PATCH"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let requestBody = ["remark": remark]
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        do {
            let (data, response) = try await URLSession.shared.data(for: request)

            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode != 200 {
                throw ItemCardError.serverError("HTTP \(httpResponse.statusCode)")
            }

            let apiResponse = try JSONDecoder().decode(APIResponse<UserItemCardAPI>.self, from: data)

            if apiResponse.success, let apiCard = apiResponse.data {
                let itemCard = ItemCard(
                    id: apiCard.id,
                    tags: apiCard.tags,
                    description: apiCard.description,
                    title: apiCard.title,
                    imageFileName: apiCard.imageFileName,
                    imageURL: apiCard.imageURL,
                    createdAt: apiCard.createdAt.toDate() ?? Date(),
                    authorId: apiCard.author.userId,
                    location: apiCard.location,
                    latitude: apiCard.latitude,
                    longitude: apiCard.longitude
                )

                let authorInfo = AuthorInfo(
                    userId: apiCard.author.userId,
                    nickname: apiCard.author.nickname,
                    avatarURL: apiCard.author.avatarURL
                )

                return UserItemCard(
                    id: apiCard.userItemCardId,
                    userId: userId,
                    cardId: apiCard.id,
                    remark: apiCard.remark,
                    acquiredAt: apiCard.acquiredAt.toDate() ?? Date(),
                    isOwner: apiCard.isOwner,
                    card: itemCard,
                    author: authorInfo
                )
            } else {
                throw ItemCardError.serverError(apiResponse.error ?? "更新备注失败")
            }

        } catch let error as ItemCardError {
            throw error
        } catch {
            if error is DecodingError {
                throw ItemCardError.decodingError(error)
            } else {
                throw ItemCardError.networkError(error)
            }
        }
    }

    /// 删除用户持有的卡片
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - cardId: 卡片ID
    func deleteUserItemCard(userId: String, cardId: String) async throws {
        guard !userId.isEmpty, !cardId.isEmpty else {
            throw ItemCardError.missingParameters
        }

        guard let url = URL(string: "\(baseURL)/userItemCard?userId=\(userId)&cardId=\(cardId)") else {
            throw ItemCardError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"

        do {
            let (data, response) = try await URLSession.shared.data(for: request)

            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode != 200 {
                throw ItemCardError.serverError("HTTP \(httpResponse.statusCode)")
            }

            let apiResponse = try JSONDecoder().decode(APIResponse<String>.self, from: data)

            if !apiResponse.success {
                throw ItemCardError.serverError(apiResponse.error ?? "删除卡片失败")
            }

        } catch let error as ItemCardError {
            throw error
        } catch {
            if error is DecodingError {
                throw ItemCardError.decodingError(error)
            } else {
                throw ItemCardError.networkError(error)
            }
        }
    }

    // MARK: - ItemCard API Methods

    /// 创建新卡片（同时会在UserItemCard中创建持有记录）
    /// - Parameters:
    ///   - title: 卡片标题
    ///   - description: 卡片描述
    ///   - tags: 标签数组
    ///   - imageFileName: 图片文件名
    ///   - imageURL: 图片URL
    ///   - authorId: 作者ID
    ///   - location: 位置信息（通过getCurrentLocationInfo获取）
    ///   - latitude: 纬度
    ///   - longitude: 经度
    /// - Returns: 创建的卡片
    func createItemCard(
        title: String,
        description: String,
        tags: [String],
        imageFileName: String,
        imageURL: String,
        authorId: String,
        location: String,
        latitude: Double?,
        longitude: Double?
    ) async throws -> ItemCard {
        guard !title.isEmpty, !description.isEmpty, !authorId.isEmpty else {
            throw ItemCardError.missingParameters
        }

        guard let url = URL(string: "\(baseURL)/itemCard?userId=\(authorId)") else {
            throw ItemCardError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let requestBody: [String: Any] = [
            "title": title,
            "description": description,
            "tags": tags,
            "imageFileName": imageFileName,
            "imageURL": imageURL,
            "location": location,
            "latitude": latitude as Any,
            "longitude": longitude as Any
        ]

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        do {
            let (data, response) = try await URLSession.shared.data(for: request)

            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode != 200 {
                throw ItemCardError.serverError("HTTP \(httpResponse.statusCode)")
            }

            let apiResponse = try JSONDecoder().decode(APIResponse<ItemCardAPI>.self, from: data)

            if apiResponse.success, let apiCard = apiResponse.data {
                return ItemCard(
                    id: apiCard.id,
                    tags: apiCard.tags,
                    description: apiCard.description,
                    title: apiCard.title,
                    imageFileName: apiCard.imageFileName,
                    imageURL: apiCard.imageURL,
                    createdAt: apiCard.createdAt.toDate() ?? Date(),
                    authorId: apiCard.authorId,
                    location: apiCard.location,
                    latitude: apiCard.latitude,
                    longitude: apiCard.longitude
                )
            } else {
                throw ItemCardError.serverError(apiResponse.error ?? "创建卡片失败")
            }

        } catch let error as ItemCardError {
            throw error
        } catch {
            if error is DecodingError {
                throw ItemCardError.decodingError(error)
            } else {
                throw ItemCardError.networkError(error)
            }
        }
    }

    // API 返回的 ItemCard 结构
    private struct ItemCardAPI: Codable {
        let id: String
        let tags: [String]
        let description: String
        let title: String
        let imageFileName: String
        let imageURL: String
        let createdAt: String
        let authorId: String
        let location: String
        let latitude: Double?
        let longitude: Double?
    }
}
