//
//  Image.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/24.
//

import SwiftUI

extension View {
    /// 使视图在被点击时可裁剪（适用于包含图片的视图）
    func croppable(_ isCroppable: Binding<Bool>, image: Binding<UIImage?>) -> some View {
        modifier(CroppableModifier(isCroppable: isCroppable, image: image))
    }
}

struct CroppableModifier: ViewModifier {
    @Binding var isCroppable: Bool
    @Binding var image: UIImage?

    func body(content: Content) -> some View {
        content
            .onTapGesture {
                if image != nil {
                    isCroppable = true
                }
            }
            .sheet(isPresented: $isCroppable) {
                if let uiImage = image {
                    ImageEditor.cropper(image: uiImage) { edited in
                        if let edited = edited {
                            image = edited
                        }
                    }
                }
            }
    }
}
