//
//  ImageProcessViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/22.
//

import SwiftUI
import PhotosUI
import Vision
import CoreImage
import Photos

@MainActor
class ImageProcessViewModel: ObservableObject {
    @Published var selectedPhoto: PhotosPickerItem?
    @Published var inputImage: UIImage?
    @Published var processedImage: UIImage?
    @Published var subjectObservation: VNInstanceMaskObservation?
    @Published var subjects: [ImageProcess.SubjectInfo] = [] // 所有检测到的主体
    @Published var selectedSubjectIndices: Set<Int> = [] // 选中的主体索引
    @Published var showPhotoPicker = false
    @Published var showSaveAlert = false
    @Published var isProcessing = false
    @Published var showPermissionAlert = false
    @Published var showSubjectSelection = false // 显示主体选择状态

    // MARK: - 图像分析相关属性
    @Published var analysisResult: ImageAnalysisResult? // 分析结果
    @Published var isAnalyzing = false // 是否正在分析
    @Published var analysisError: String? // 分析错误信息

    private let imageProcessingService = ImageProcess()
    private let geminiService = GeminiImageAnalysisService() // Gemini分析服务

    @MainActor
    func checkPhotoLibraryPermission() async -> Bool {
        let status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        switch status {
        case .authorized, .limited:
            return true
        case .notDetermined:
            return await withCheckedContinuation { continuation in
                PHPhotoLibrary.requestAuthorization(for: .readWrite) { newStatus in
                    continuation.resume(returning: newStatus == .authorized || newStatus == .limited)
                }
            }
        case .denied, .restricted:
            showPermissionAlert = true
            return false
        @unknown default:
            return false
        }
    }

    @MainActor
    func loadImage() {
        Task {
            // 检查相册权限
            guard await checkPhotoLibraryPermission() else {
                print("相册权限未授予")
                return
            }

            guard let selectedPhoto = selectedPhoto,
                  let data = try? await selectedPhoto.loadTransferable(type: Data.self),
                  let uiImage = UIImage(data: data) else {
                print("无法加载图片数据")
                return
            }

            inputImage = uiImage
            // 检测主体
            detectSubjects(in: uiImage)
        }
    }
    
    // MARK: 检测主体的方法
    /// 重新检测主体（用于图像编辑后）
    @MainActor
    func reDetectSubjects() {
        guard let inputImage = inputImage else {
            // 如果没有图像，清空主体相关数据
            subjectObservation = nil
            subjects = []
            showSubjectSelection = false
            return
        }
        
        // 检测主体
        detectSubjects(in: inputImage)
    }
    
    /// 检测主体的通用方法
    private func detectSubjects(in image: UIImage) {
        if let observation = imageProcessingService.detectSubjects(in: image) {
            subjectObservation = observation
            // 计算所有主体的中心位置
            subjects = imageProcessingService.calculateSubjectCenters(from: observation)
            showSubjectSelection = !subjects.isEmpty
        } else {
            subjectObservation = nil
            subjects = []
            showSubjectSelection = false
        }

        // 清空处理后的图像和选择索引
        processedImage = nil
        selectedSubjectIndices.removeAll()
    }

    // MARK: - 处理主体选择
    @MainActor
    func handleImageTap(at position: CGPoint) {
        // 查找最近的主体
        if let nearestSubject = imageProcessingService.findNearestSubject(at: position, in: subjects) {
            toggleSubjectSelection(nearestSubject.index)
        }
    }

    @MainActor
    func toggleSubjectSelection(_ index: Int) {
        if selectedSubjectIndices.contains(index) {
            selectedSubjectIndices.remove(index)
        } else {
            selectedSubjectIndices.insert(index)
        }
    }

    @MainActor
    func selectAllSubjects() {
        selectedSubjectIndices = Set(subjects.map { $0.index })
    }

    @MainActor
    func clearSelection() {
        selectedSubjectIndices.removeAll()
    }

    @MainActor
    func processImage() {
        guard let inputImage = inputImage else { return }
        isProcessing = true

        Task {
            var extractedImage: UIImage?

            if selectedSubjectIndices.isEmpty {
                // 如果没有选择主体，提取所有主体
                extractedImage = imageProcessingService.extractSubject(
                    from: inputImage,
                    croppedToInstancesExtent: true
                )
            } else {
                // 提取选中的主体
                extractedImage = imageProcessingService.extractMultipleSubjects(
                    from: inputImage,
                    selectedIndices: selectedSubjectIndices,
                    croppedToInstancesExtent: true
                )
            }

            processedImage = extractedImage
            isProcessing = false

            // 主体提取完成后，自动分析图像内容
            if let extractedImage = extractedImage {
                await analyzeExtractedImage(extractedImage)
            }
        }
    }

    // MARK: - 图像分析功能
    /// 分析提取后的图像内容
    /// - Parameter image: 提取后的图像
    @MainActor
    private func analyzeExtractedImage(_ image: UIImage) async {
        isAnalyzing = true
        analysisError = nil

        // 获取用户设置的API key（如果有的话）
        // 注意：这里直接创建AppSettings实例，在未来版本中可以考虑通过依赖注入优化
        let appSettings = AppSettings()
        let customApiKey = appSettings.geminiApiKey.isEmpty ? nil : appSettings.geminiApiKey

        // 调用Gemini API分析图像
        let result = await geminiService.analyzeImage(image, customApiKey: customApiKey)

        if let result = result {
            analysisResult = result
            print("图像分析完成: Tags=\(result.Tags), Description=\(result.Description)")
        } else {
            analysisError = geminiService.errorMessage ?? "分析失败"
            print("图像分析失败: \(analysisError ?? "未知错误")")
        }

        isAnalyzing = false
    }

    @MainActor
    func saveImage() {
        guard let processedImage = processedImage else { return }
        imageProcessingService.saveImageToAlbum(processedImage) { success, error in
            if success {
                self.showSaveAlert = true
            }
        }
    }
}
