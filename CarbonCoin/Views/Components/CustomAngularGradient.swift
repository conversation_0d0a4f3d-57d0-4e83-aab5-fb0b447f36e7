//
//  CustomAngularGradient.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/16.
//

import SwiftUI

struct CustomAngularGradient: View {
    private let gradient: Gradient
    private let center: UnitPoint
    private let angle: Angle
    
    init(
        stops: [(color: Color, location: Double)] = [
            (Color(hex: "010101"), 0.00),
            (Color(hex: "000000"), 0.0048),
            (Color(hex: "2E4F17"), 0.3699),
            (Color(hex: "2A370C"), 0.5240),
            (Color(hex: "010101"), 1.0)
        ],
        center: UnitPoint = UnitPoint(x: 0.4281, y: 0.4891),
        angle: Angle = Angle(degrees: 90)
    ) {
        self.gradient = Gradient(stops: stops.map { Gradient.Stop(color: $0.color, location: $0.location) })
        self.center = center
        self.angle = angle
    }
    
    var body: some View {
        AngularGradient(gradient: gradient, center: center, angle: angle)
            .ignoresSafeArea() // 覆盖整个屏幕，包括状态栏和安全区域
    }
}
