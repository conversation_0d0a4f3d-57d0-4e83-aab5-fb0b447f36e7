//
//  ItemCardLibrary.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/24.
//

import SwiftUI

struct ItemCardLibrary: View {
    @EnvironmentObject var cardStore: CardStore
    @State private var searchText = ""
    @State private var selectedCard: ItemCard?
    @State private var showCardDetail = false
    @Namespace private var cardTransition

    // 过滤后的卡片
    private var filteredCards: [ItemCard] {
        if searchText.isEmpty {
            return cardStore.cards.sorted { $0.createdAt > $1.createdAt }
        } else {
            return cardStore.cards
                .filter { $0.title.localizedCaseInsensitiveContains(searchText) }
                .sorted { $0.createdAt > $1.createdAt }
        }
    }

    var body: some View {
        NavigationStack {
            ZStack {
                CustomAngularGradient()

                VStack(spacing: 0) {
                    // 搜索栏
                    searchBar
                        .padding(.horizontal, Theme.Spacing.md)
                        .padding(.top, Theme.Spacing.sm)

                    if filteredCards.isEmpty {
                        emptyStateView
                    } else {
                        // 卡片网格
                        ScrollView {
                            LazyVGrid(columns: [
                                GridItem(.flexible(), spacing: Theme.Spacing.sm),
                                GridItem(.flexible(), spacing: Theme.Spacing.sm)
                            ], spacing: Theme.Spacing.md) {
                                ForEach(filteredCards) { card in
                                    ItemCardThumbnailView(card: card)
                                }
                            }
                            .padding(.horizontal, Theme.Spacing.md)
                            .padding(.bottom, Theme.Spacing.tab)
                            .padding(.top, Theme.Spacing.lg)
                        }
                        .scrollContentBackground(.hidden)
                    }
                    
                }
            }
            .navigationTitle("卡片库")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
        }
    }

    // MARK: - 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.textSecondary)

            TextField("搜索卡片标题...", text: $searchText)
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)
        }
        .padding(.horizontal, Theme.Spacing.md)
        .padding(.vertical, Theme.Spacing.sm)
        .background(Color.cardBackground.opacity(0.3))
        .cornerRadius(Theme.CornerRadius.md)
        .glassCard()
    }

    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: Theme.Spacing.lg) {
            Spacer()

            Image(systemName: "folder")
                .font(.system(size: 60))
                .foregroundColor(.textSecondary)

            VStack(spacing: Theme.Spacing.sm) {
                Text(searchText.isEmpty ? "暂无卡片" : "未找到匹配的卡片")
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)

                Text(searchText.isEmpty ? "使用图像处理功能创建您的第一张卡片" : "尝试使用其他关键词搜索")
                    .font(.bodyBrand)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
            }

            Spacer()
        }
        .padding(.horizontal, Theme.Spacing.lg)
    }
}

#Preview {
    let cardStore = CardStore()
    ItemCardLibrary()
        .environmentObject(cardStore)
}
