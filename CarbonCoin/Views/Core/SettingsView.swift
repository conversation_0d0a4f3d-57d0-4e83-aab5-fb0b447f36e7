//
//  SettingsView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/20.
//

import SwiftUI
import UIKit

struct SettingsView: View {
    @EnvironmentObject var appSettings: AppSettings
    
    @State private var showImagePicker = false
    @State private var selectedImage: UIImage?
    @State private var showAvatarActionSheet = false
    @State private var shareLocation = true
    @State private var pushNotifications = true
    
    // MARK: 设置页主体
    var body: some View {
        NavigationStack{
            ZStack{
                CustomAngularGradient()
                
                ScrollView{
                    VStack(spacing: Theme.Spacing.lg){
                        // 用户信息卡片
                        AvatarCard()
                            .frame(width: UIScreen.main.bounds.width * 0.8)
                        
                        UserInfoSection()
                        
                        // 隐私设置
                        PrivacySettingsSection()
                        
                        // API设置
                        APISettingsSection()

                        // 其他设置
                        OtherSettingsSection()
                        
                        Spacer(minLength: Theme.Spacing.tab) // 为底部TabBar留出空间
                    }
                    .padding(.horizontal, Theme.Spacing.md)
                    .padding(.top, Theme.Spacing.md)
                }
            }
            .navigationTitle("设置")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
        }
    }

}

// MARK: 用户头像与名称信息
struct AvatarCard: View{
    @EnvironmentObject var appSettings: AppSettings
    private var avatarSize: CGFloat = 80
    
    @State private var showImagePicker = false
    @State private var selectedImage: UIImage?
    @State private var showAvatarActionSheet = false
    
    var body: some View{
        ZStack{
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .fill(Color.cardBackground)
            
            VStack(spacing: 4){
                Button(action:{
                    showAvatarActionSheet = true
                })
                {
                    appSettings.settings.getAvatarSwiftUIImage()
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: avatarSize, height: avatarSize)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                }
                
                 
                Text(appSettings.settings.nickname)
                        .font(.title3Brand)
                        .padding(.top, 8)
                
                
                Text(appSettings.settings.userId)
                    .font(.captionBrand)
    
            }
        }
        .frame( height: 180)
        .glassCard()
        .actionSheet(isPresented: $showAvatarActionSheet) {
            ActionSheet(
                title: Text("选择头像"),
                buttons: [
                    .default(Text("使用系统默认")) {
                        Task {
                            await appSettings.setCustomAvatarWithSync(from: nil)
                        }
                    },
                    .default(Text("从相册选择")) {
                        showImagePicker = true
                    },
                    .cancel()
                ]
            )
        }
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(selectedImage: $selectedImage)
        }
        .onChange(of: selectedImage) { _, newImage in
            if let image = newImage {
                Task {
                    await appSettings.setCustomAvatarWithSync(from: image)
                }
            }
        }
    }
}

// 图片选择器
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.presentationMode) var presentationMode
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}


// MARK: 个人信息编辑
struct UserInfoSection: View{
    @EnvironmentObject var appSettings: AppSettings

    @State private var isEditingNickname = false
    @State private var tempNickname = ""
    @FocusState private var isNicknameFocused: Bool

    var body: some View{
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
                    // 昵称行
                    HStack {
                        Image(systemName: "person.text.rectangle")
                            .foregroundColor(.blue.opacity(0.8))

                        Text("昵称")
                            .foregroundColor(.primary)

                        Spacer()

                        TextField("昵称", text: $tempNickname)
                            .multilineTextAlignment(.trailing)
                            .foregroundColor(.secondary)
                            .focused($isNicknameFocused)
                            .onSubmit {
                                // 用户按回车键时提交更改
                                commitNicknameChange()
                            }
                    }

                    // 用户ID
                    HStack {
                        Image(systemName: "person.text.rectangle")
                            .foregroundColor(.cyan.opacity(0.8))

                        Text("用户ID")
                            .foregroundColor(.primary)

                        Spacer()

                        Text(appSettings.userId)
                            .multilineTextAlignment(.trailing)
                            .foregroundColor(.secondary)
                    }

                }
                .padding(Theme.Spacing.lg)
                .glassCard()
                .onAppear {
                    // 初始化临时昵称
                    tempNickname = appSettings.settings.nickname
                }
                .onChange(of: isNicknameFocused) { _, isFocused in
                    if !isFocused && tempNickname != appSettings.settings.nickname {
                        // 失去焦点时提交更改
                        commitNicknameChange()
                    }
                }
    }

    /// 提交昵称更改
    private func commitNicknameChange() {
        guard !tempNickname.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty,
              tempNickname != appSettings.settings.nickname else {
            // 如果昵称为空或没有变化，恢复原值
            tempNickname = appSettings.settings.nickname
            return
        }

        // 更新本地设置并触发云端同步
        appSettings.nickname = tempNickname.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}


// MARK: 隐私设置
struct PrivacySettingsSection: View {
    @EnvironmentObject var appSettings: AppSettings
    @AppStorage("currentUserId") private var currentUserId: String = ""
    @State private var shareLocation: Bool = true
    @State private var pushNotifications: Bool = true

    private let userInfoService = UserInfoUpdateService.shared

    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("隐私设置")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            VStack(spacing: Theme.Spacing.md) {
                SettingToggleRow(
                    icon: "location.fill",
                    title: "位置共享",
                    description: "允许好友查看我的位置",
                    isOn: $shareLocation
                )
                .onChange(of: shareLocation) { _, newValue in
                    // 位置共享设置变化时同步到云端
                    Task {
                        await syncSharingLocationToCloud(newValue)
                    }
                }

                SettingToggleRow(
                    icon: "bell.fill",
                    title: "推送通知",
                    description: "接收好友消息和系统通知",
                    isOn: $pushNotifications
                )
            }
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
        .onAppear {
            // 初始化设置值
            shareLocation = true // 可以从AppSettings或其他地方获取初始值
            pushNotifications = true
        }
    }

    /// 同步位置共享设置到云端
    private func syncSharingLocationToCloud(_ sharingLocation: Bool) async {
        guard !currentUserId.isEmpty else {
            print("⚠️ 用户未登录，跳过位置共享设置同步")
            return
        }

        do {
            _ = try await userInfoService.updateSharingLocation(
                userId: currentUserId,
                sharingLocation: sharingLocation
            )
            print("✅ 位置共享设置已同步到云端: \(sharingLocation)")
        } catch {
            print("❌ 位置共享设置同步到云端失败: \(error.localizedDescription)")
        }
    }
}

// MARK: - API设置
struct APISettingsSection: View {
    @EnvironmentObject var appSettings: AppSettings
    @State private var showApiKeyInput = false
    @State private var tempApiKey = ""
    @State private var showSaveAlert = false

    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("API设置")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            VStack(spacing: Theme.Spacing.sm) {
                // Gemini API Key设置
                Button(action: {
                    tempApiKey = appSettings.geminiApiKey
                    showApiKeyInput = true
                }) {
                    HStack {
                        Image(systemName: "key.fill")
                            .foregroundColor(.auxiliaryYellow)
                            .frame(width: 24)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("Gemini API Key")
                                .font(.bodyBrand)
                                .foregroundColor(.textPrimary)

                            Text(appSettings.geminiApiKey.isEmpty ? "未设置" : "已设置 (\(appSettings.geminiApiKey.prefix(8))...)")
                                .font(.captionBrand)
                                .foregroundColor(.textSecondary)
                        }

                        Spacer()

                        Image(systemName: "chevron.right")
                            .foregroundColor(.textSecondary)
                            .font(.caption)
                    }
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.vertical, Theme.Spacing.xs)

                // API使用说明
                VStack(alignment: .leading, spacing: 4) {
                    Text("说明")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)

                    Text("• 用于图像内容分析和标签识别")
                        .font(.caption2)
                        .foregroundColor(.textSecondary)

                    Text("• 如不设置将使用默认密钥（功能受限）")
                        .font(.caption2)
                        .foregroundColor(.textSecondary)
                }
                .padding(.top, Theme.Spacing.xs)
            }
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
        .sheet(isPresented: $showApiKeyInput) {
            APIKeyInputSheet(
                apiKey: $tempApiKey,
                onSave: {
                    appSettings.geminiApiKey = tempApiKey
                    showSaveAlert = true
                }
            )
        }
        .alert("保存成功", isPresented: $showSaveAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text("API Key已保存")
        }
    }
}

// MARK: - API Key输入界面
struct APIKeyInputSheet: View {
    @Binding var apiKey: String
    let onSave: () -> Void
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationStack {
            VStack(spacing: Theme.Spacing.lg) {
                VStack(alignment: .leading, spacing: Theme.Spacing.md) {
                    Text("设置Gemini API Key")
                        .font(.title2Brand)
                        .foregroundColor(.textPrimary)

                    Text("请输入您的Google Gemini API密钥，用于图像内容分析功能。")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                        .multilineTextAlignment(.leading)
                }

                VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                    Text("API Key")
                        .font(.bodyBrand)
                        .foregroundColor(.textPrimary)

                    TextField("请输入API Key", text: $apiKey)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .font(.bodyBrand)
                }

                VStack(alignment: .leading, spacing: 8) {
                    Text("获取API Key:")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)

                    Text("1. 访问 Google AI Studio")
                        .font(.caption2)
                        .foregroundColor(.textSecondary)

                    Text("2. 创建新的API密钥")
                        .font(.caption2)
                        .foregroundColor(.textSecondary)

                    Text("3. 复制密钥并粘贴到此处")
                        .font(.caption2)
                        .foregroundColor(.textSecondary)
                }
                .padding(.top, Theme.Spacing.md)

                Spacer()
            }
            .padding(Theme.Spacing.lg)
            .navigationTitle("API设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        onSave()
                        dismiss()
                    }
                    .disabled(apiKey.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
    }
}


// MARK: 其他信息与设置
struct OtherSettingsSection: View {
    @EnvironmentObject var appSettings: AppSettings
    
    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("其他")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            VStack(spacing: Theme.Spacing.sm) {
                SettingRow(icon: "info.circle", title: "关于应用", subtitle: "版本 1.0.0")
                SettingRow(icon: "envelope", title: "意见反馈", subtitle: "")
                SettingRow(icon: "questionmark.circle", title: "帮助中心", subtitle: "")
                SettingRow(icon: "arrow.right.square", title: "退出登录", subtitle: "", isDestructive: true)
                Text("CarbonCoin已经陪伴您 \(appSettings.settings.appUsageDays) 天了")
                    .font(.captionBrand)
                    .foregroundColor(.secondary)
            }
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}


// MARK: - Setting Toggle Row
struct SettingToggleRow: View {
    let icon: String
    let title: String
    let description: String
    @Binding var isOn: Bool

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.auxiliaryYellow)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)

                Text(description)
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }

            Spacer()

            Toggle("", isOn: $isOn)
                .toggleStyle(SwitchToggleStyle(tint: Color.brandGreen))
        }
    }
}

// MARK: - Setting Row
struct SettingRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let isDestructive: Bool

    init(icon: String, title: String, subtitle: String, isDestructive: Bool = false) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.isDestructive = isDestructive
    }

    var body: some View {
        Button(action: {}) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(isDestructive ? .error : .auxiliaryYellow)
                    .frame(width: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.bodyBrand)
                        .foregroundColor(isDestructive ? .error : .textPrimary)

                    if !subtitle.isEmpty {
                        Text(subtitle)
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary)
                    }
                }

                Spacer()

                if !isDestructive {
                    Image(systemName: "chevron.right")
                        .foregroundColor(.textSecondary)
                        .font(.caption)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .padding(.vertical, Theme.Spacing.xs)
    }
}



#Preview {
    let appSettings = AppSettings()
    SettingsView()
        .environmentObject(appSettings)
}
