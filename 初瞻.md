## 1. 项目概述

“碳市”是一款旨在通过游戏化方式推动绿色生活、实现碳排放可视化的移动应用。核心目标用户为Z世代和高校学生。通过可爱的UI风格、丰富的互动玩法和社交元素，让用户将环保行为内化为“日常游戏”中的成长与奖励。

**核心风格关键词：** 数字孪生 / AR轻拟物 / 萌感生态 / 塑胶微光质感



## 2. 主要功能及SwiftUI技术分析

### 2.1 用户界面与核心设计

- **视觉风格：** 圆润、可爱、富有亲和力的角色和界面，消解专业名词的陌生感。

- **色彩系统：** 以自然绿为主色，辅以活力黄（奖励）、科技黑（地图背景）、清新白（界面通透），并应用碳币绿到天空蓝的渐变。

- **UI组件：** 圆润高光质感的按钮、浮空微透明玻璃质感的卡片、半拟物风格图标。

- **主视觉元素：** 3D地球模型（动态旋转）、可爱白色精灵。

  **SwiftUI技术分析：**

  - **UI布局与样式：** SwiftUI的声明式语法（`VStack`, `HStack`, `ZStack`, `GeometryReader`等）非常适合构建复杂的自定义布局。
  - **自定义形状与动画：** `Shape`协议、`Path` API、`modifier`以及各种动画（`withAnimation`, `matchedGeometryEffect`, `transition`）可以轻松实现圆润的UI元素、按钮的高光/浮空效果、卡片的玻璃质感、图标的半拟物风格及相关动画。
  - **色彩与渐变：** `Color`和`Gradient`类型直接支持定义和应用项目色彩系统中的所有颜色和渐变。
  - **字体：** `Font`对象可灵活设置字体样式、字重和大小。若需自定义字体，可通过`Font.custom()`集成。
  - **3D模型与精灵：** 3D地球模型和可爱的白色精灵需要集成`SceneKit`或`RealityKit`框架。SwiftUI可以通过`UIViewRepresentable`或`ARViewContainer`（若有3D交互）来承载这些视图。精灵的动画则可在SwiftUI或其集成的框架中实现。

### 2.2 地图交互与轨迹记录

- **地图探索：** 用户在城市中探索低碳“打卡点”和“城市绿色宝藏”。

- **轨迹记录：** 用户出行轨迹的记录与显示。

- **Zenly式好友地图互动：** 在地图上显示好友位置，并可能展示其动态，提供有趣的社交互动。

  **SwiftUI技术分析：**

  - **地图显示：** 使用`Map`视图（iOS 14+）或`MKMapViewRepresentable`来集成Apple的MapKit框架，显示2D地图。**无需额外准备基础地图数据，但自定义的打卡点、宝藏、用户轨迹等数据需自行准备。**
  - **位置追踪：** `CoreLocation`框架用于获取用户实时位置、记录出行轨迹。
  - **轨迹绘制：** 获取到的位置点可以组合成`MKPolyline`叠加在`MKMapView`上，实现轨迹的绘制和显示。
  - **自定义地图元素：** 利用`MKAnnotation`自定义打卡点和宝藏的地图标记（如自定义图片、动画）。`MKOverlayRenderer`用于自定义轨迹的渲染样式。
  - **Zenly式交互：** 实现此类复杂的实时交互、动画和自定义标记，原生SwiftUI配合MapKit具有显著优势。虽然H5可以展示地图，但要达到Zenly级别的高性能、流畅和自定义交互，原生实现更为可靠和高效。可能需要结合SwiftUI的动画系统和自定义视图来制作生动的好友标记。

### 2.3 核心玩法与宠物系统

- **碳币获取：** 通过探索打卡点、完成任务获取“碳币”。

- **碳宠系统：** 收集、进化和个性化“碳宠”（参考《山海经》异兽），并在出行、拍照界面中融入更多宠物元素。

- **奖励与反馈：** 完成任务或获得碳币时提供即时视觉正反馈。奖励页面普及环保知识。

  **SwiftUI技术分析：**

  - **数据模型：** 定义`CarbonCoin`、`Task`、`CarbonPet`等数据模型，遵循`Codable`和`Identifiable`协议，方便数据管理和UI更新。
  - **任务与奖励逻辑：** 在应用逻辑层实现任务完成、碳币计算、奖励发放等业务逻辑。
  - **宠物集成：** 宠物形象可作为`Image`视图或更复杂的`SceneKit`/`RealityKit`视图嵌入到UI中。SwiftUI的动画和过渡效果可用于宠物的进化、互动动画。
  - **视觉反馈：** `Alert`、`Sheet`、自定义`Toast`视图以及`UIImpactFeedbackGenerator`/`UINotificationFeedbackGenerator`（触觉反馈）可用于提供即时、多感官的反馈。

### 2.4 社交功能

- **好友动态：** 分享沿途风景。

- **实时聊天：** 好友间实时交流。

- **排行榜：** 用户间的碳币和减碳成就排名。

- **成就分享：** 分享减碳成果到社交网络。

  **SwiftUI技术分析：**

  - **后端服务：** 核心社交功能（好友关系、聊天消息、排行榜数据、动态发布与存储）**强烈依赖后端服务器**（如Firebase、CloudKit或自定义REST API）。
  - **网络请求：** 使用`URLSession`或第三方网络库与后端进行数据交互。
  - **列表与数据展示：** `List`和`ForEach`视图用于高效展示好友列表、聊天记录、排行榜数据等。
  - **实时更新：** 对于聊天等实时功能，可能需要利用WebSocket或后端提供的实时数据库（如Firestore）集成。
  - **图片分享：** 使用`ShareLink`（iOS 16+）或通过`UIViewControllerRepresentable`封装`UIActivityViewController`实现内容分享到系统社交平台。



### 2.5 碳足迹与商品扫描

- **碳足迹追踪：** 记录日常低碳行为（节水、节电、减碳出行等）。

- **商品碳影响扫描：** 输入商品图片/条形码，分析其碳排放并获得奖励。

  **SwiftUI技术分析：**

  - **数据记录与统计：** 应用内数据模型和逻辑层负责记录用户的各种低碳行为数据，并进行多维度统计。
  - **摄像头集成：** `AVFoundation`框架用于访问摄像头，实现拍照和条形码扫描功能。这需要通过`UIViewControllerRepresentable`在SwiftUI中进行封装。
  - **图像识别/条形码扫描：** `Vision`框架用于识别条形码和图像内容。
  - **碳影响分析：** 这部分的逻辑将是核心，需要根据商品信息（通过扫描获取）调用后端服务或本地数据库来计算其碳排放量和环保属性（运输、包装、生产等）。



## 3. 技术栈总结



| 技术领域      | 技术栈/库                                                    | 说明                                                         |
| ------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| **前端框架**  | SwiftUI                                                      | Apple官方声明式UI框架，用于构建应用的用户界面和交互，提供流畅、高性能的原生体验。 |
| **地图服务**  | MapKit                                                       | Apple的地图框架，用于显示2D地图、自定义地图标记（打卡点、宝藏、好友位置）和绘制用户轨迹。 |
| **定位服务**  | CoreLocation                                                 | 用于获取用户实时位置数据，支持轨迹记录和基于位置的互动功能。 |
| **3D图形/AR** | SceneKit / RealityKit                                        | 用于渲染3D地球模型和可爱的白色精灵，实现动态效果。如果未来需要简单的3D宠物元素，也可能用到。 |
| **媒体处理**  | AVFoundation                                                 | 用于访问设备摄像头，实现商品拍照和条形码扫描功能。           |
| **图像识别**  | Vision                                                       | 用于高效识别和解析条形码，可能用于图像识别以分析商品信息。   |
| **用户体验**  | UIKit (通过`Representable`封装)                              | 某些特定功能（如`UIActivityViewController`用于分享，复杂的自定义视图或某些触觉反馈）可能需要通过SwiftUI的`UIViewControllerRepresentable`或`UIViewRepresentable`进行桥接。 |
| **网络通信**  | URLSession                                                   | Apple内置的网络API，用于与后端服务器进行数据交互（如获取打卡点、上传轨迹、社交互动）。 |
| **后端支持**  | **（推荐以下之一或组合）**                                   | 用于存储和管理用户数据、游戏状态、社交关系、排行榜、生成打卡点/宝藏的算法，以及商品碳排放数据库。 |
|               | CloudKit                                                     | Apple提供的一站式后端服务，适合iOS生态，提供数据存储、用户认证、实时同步等。 |
|               | Firebase (Firestore/Realtime Database, Authentication, Cloud Functions等) | Google提供的移动后端即服务（MBaaS），功能全面，易于集成，适合快速开发和实时数据需求。 |
|               | 自定义后端服务 (如 Node.js, Python/Django/Flask, Go, Java/Spring Boot 等) | 提供最大灵活性和可扩展性，可根据需求定制所有API和业务逻辑，但需要自行搭建和维护服务器。 |
| **数据存储**  | Core Data / Realm / SQLite (客户端本地)                      | 用于在客户端本地持久化存储用户偏好、缓存数据或离线数据等。大规模共享数据主要依赖后端。 |







## 合适的提示词

```
# 通用开发准则
	•	使用 MARK: 分区以提升代码可读性。
	•	避免视图中出现业务逻辑代码。
	•	所有颜色和字体样式通过统一命名管理。
	•	不要硬编码字符串、图片名或颜色。
	•	所有功能组件应支持预览（PreviewProvider）。
	•	避免视图直接访问服务或模型层。
	•	状态应只由 ViewModel 控制。
	•	保持文件命名与功能一致，清晰可搜索。
	
# 项目架构与文件组织
请参考下面的文件夹结构管理文件:

##  Models/
	•	所有模型应遵循 Codable 和 Identifiable 协议。
	•	模型结构应紧贴接口返回结构。
	•	使用简洁字段名与后端字段保持一致。
	•	保持模型纯净，不包含业务逻辑。
	•	若存在转换逻辑，应在专用转换器中处理。


## ViewModels/
	•	ViewModel 应仅负责状态与业务逻辑处理。
	•	使用 @Published 管理可观察状态。
	•	初始化时注入服务层接口，避免直接调用实现类。
	•	异步任务使用 async/await 统一处理。
	•	错误信息与加载状态应有独立状态字段。
	•	ViewModel 文件命名应为 [Feature]ViewModel.swift。

## Services/
	•	每个服务应定义协议并提供实现类。
	•	所有网络请求（如使用 URLSession）应统一封装到服务中，ViewModel 不应直接处理网络请求。
	•	不直接在 ViewModel 中处理 URLSession。
	•	接口路径、参数、响应应抽象为结构体。
	•	提供默认实现与 Mock 实现以便测试。
	•	不直接使用第三方 API，应通过适配封装。

## Protocols/
	•	所有协议命名应以 Protocol 或 Delegate 结尾。
	•	保持接口粒度小、职责单一。
	•	协议需配合默认实现使用扩展定义。
	•	不允许冗余定义未使用的协议方法。
	•	用于约束服务、组件、状态行为的抽象。

## Views/
	•	所有 View 应为纯展示组件。
	•	UI 控件绑定状态由 ViewModel 提供。
	•	每个主页面应拆分为多个子组件。
	•	命名采用 [Feature]View.swift 统一规范。
	•	尽量避免 .onAppear 做过多逻辑。
	•	支持浅色、深色模式与动态字体。

## Resources/
	•	所有颜色应在 Asset 中定义并语义命名。
	•	图片资源统一前缀命名如 icon_、bg_。
	•	避免直接使用字符串调用资源。
	•	字体、图标、颜色统一封装为静态访问。
	•	所有资源应支持 Dark Mode 适配。

## Styles/
	•	颜色命名使用语义词如 primary, secondary。
	•	字体样式统一封装为静态常量。
	•	所有间距使用统一命名常量，如 spacingM。
	•	避免在组件中直接设置 size 或 color 值。
	•	样式类文件命名为 Theme.swift 或 StyleGuide.swift。

## Utilities/
	•	工具函数必须具备明确单一功能。
	•	所有扩展分类应按类型命名如 Date+Format.swift。
	•	避免 ViewModel 中定义工具方法。
	•	错误处理、数据格式化集中封装。
	•	公共常量统一收纳于 Constants 文件中。
```



## 其他问题

- 什么时候记录？需要一直在后台记录吗？ 还是说主动打开app之后再开始记录轨迹？

- **碳宠物的养成系统**：关于碳宠物的收集和进化，具体逻辑是怎样的？
  - 例如，进化是基于用户等级、收集的碳币数量，还是需要特定的“进化石”道具？这个系统的复杂度会直接影响Model层和ViewModel层的设计
