# “碳市”MVP阶段性任务规划（SwiftUI + MVVM架构，基于CloudKit后端）

基于您的最新要求，本方案全面切换到使用CloudKit作为后端服务（存储用户数据、好友关系、位置共享、碳币、道具事件、宠物状态等），不再依赖全Mock数据（轨迹记录使用本地Core Data + CloudKit同步，其他数据直接从CloudKit CRUD）。任务规划按照页面层级组织：每个主要阶段对应一个页面（足迹、宠物、扫码、聊天、设置），以确保开发渐进聚焦。阶段0为全局初始化，阶段6为集成优化。每个阶段包括详细任务清单（分步骤）、技术实现 & 思路（具体代码思路、技术栈、CloudKit集成细节）、注意事项（潜在问题、优化点）。整体风格：可爱UI（圆润按钮、玻璃卡片、绿蓝渐变）、MVVM（View绑定ViewModel，ViewModel调用Service/Repository）。

总时长约9周，假设2-3人团队。数据模型统一遵循Codable/Identifiable，支持CloudKit CKRecord转换。位置共享需用户同意，道具系统设计为可扩展（enum + customData）。宠物Model支持未来进化（level/exp字段），但MVP不实现进化逻辑。扫码页面实现条形码扫描，但UI留白未来AR。聊天页面完全留白（空View + 提示）。设置页面包括好友添加（用户名搜索）。

## 阶段 0: 项目初始化 & 全局架构搭建（1 周）

此阶段建立MVVM基础、CloudKit集成、共享组件，确保后续页面独立开发。

### 任务清单

1. 创建SwiftUI项目，支持iOS 17+（启用MapKit/动画API）。
2. 配置主导航：TabView（5个Tab：足迹、宠物、扫码、聊天、设置），每个Tab内嵌NavigationStack。
3. 搭建MVVM目录结构：
   - Models：全局模型（如User、TrackPoint、Track、CarbonPet、PropEvent、ScanResult）。
   - ViewModels：每个页面一个VM（如FootprintViewModel）。
   - Views：共享组件（如RoundedButton、GlassCard、GradientBackground）。
   - Services：封装（如LocationService、CloudKitService、ScanService）。
   - Repositories：数据层（如TrackRepository、FriendRepository、CoinRepository）。
4. 集成CloudKit：在Xcode Capabilities启用iCloud/CloudKit，创建默认容器（CKContainer.default()）。
5. 编写CloudKitService：提供通用CRUD接口（saveRecord、fetchRecords、queryWithPredicate、subscribeToChanges），支持CKRecord <-> Model转换。
6. 定义核心数据模型（示例）：
   - User: id (CKRecord.ID), username, carbonCoins: Int, location: CLLocation?。
   - PropEvent: id, type: PropType (enum: briefAnimation, persistentEffect, textDisplay, with customData: [String: Any]? for扩展)。
   - CarbonPet: id, level: Int = 1, exp: Int = 0, feedCost: Int。
7. 创建AppConfig.swift：常量（如位置采样5秒、道具duration: 5-30秒、碳币奖励5-10、CloudKit查询频率10秒）。
8. 实现认证：使用CloudKit用户发现（CKCurrentUserDefaultName），Mock开发时用匿名用户。
9. 构建共享UI组件：RoundedButton (Shape+highLight), GlassCard (Blur+Gradient), 色彩系统 (Color.green主色, Gradient.greenToBlue)。

### 技术实现 & 思路

- **技术栈**：SwiftUI (VStack/HStack/ZStack/GeometryReader for布局), CloudKit (CKDatabase for public/private数据库，CKQuery for查询，CKSubscription for实时更新), Core Data (仅轨迹本地持久化，NSManagedObjectSubclass)。
- **思路**：ViewModel使用@Published属性绑定UI，async/await调用CloudKitService（e.g., func fetchUser() async throws -> User { let record = try await database.fetch(withRecordID: userID); return User(from: record) }）。Model扩展：init(from record: CKRecord), toRecord()。初始数据：App启动时查询CloudKit用户记录，若无则创建（saveRecord）。UI动画：withAnimation(.easeInOut) for按钮反馈。测试：用CKContainer.simulator for模拟环境。
- **注意事项**：处理CloudKit错误（CKError.networkUnavailable用本地缓存fallback）。权限：请求NSLocationWhenInUseUsageDescription。数据隐私：位置仅private数据库。开发时用Mock数据填充CloudKit dashboard测试。

## 阶段 1: 足迹页面开发（2 周）

聚焦足迹页面：地图入口、轨迹脚印、运动信息（碳足迹图表、每日/周/月统计，如图像参考的图表+百分比） 、好友位置共享、道具施加。集成图像风格：暗绿背景、圆润卡片、图表。

### 任务清单

1. 设计页面布局：顶部用户问候（"Hi, [username]" + 碳币显示 + 百分比箭头），中部公告卡片（蓝框式，如"敏感地图已经维修完毕！"），底部图表Tab (24h/Week/Month/6Month) + 线图/柱图，地图入口按钮。
2. 集成MapKit：MKMapViewRepresentable显示2D地图，用户轨迹脚印（MKPolyline + 脚印纹理）。
3. LocationService：请求位置权限，每5秒获取/上传到CloudKit (FriendLocation记录)。
4. TrackRepository：本地Core Data存储Track/TrackPoint，同步CloudKit (save as CKRecord with points array)。
5. 绘制轨迹：抽稀算法 (>10m添加点)，用MKOverlayRenderer渲染脚印。
6. 运动信息：从CloudKit查询碳足迹数据（e.g., dailySteps, carbonSaved），生成图表 (SwiftUI Chart or UIKit桥接)。
7. 好友位置：查询CloudKit FriendLocation，显示MKAnnotation (头像)。
8. 道具施加：好友标记长按菜单，选择PropType，save PropEvent到CloudKit。
9. 道具播放：订阅CloudKit变化，收到事件播放动画 (brief: Canvas粒子; persistent: Glow overlay; text: Text bubble)。
10. UI细节：渐变图表 (绿填充)，可爱图标 (脚印/地球)。

### 技术实现 & 思路

- **技术栈**：MapKit (MKMapView/MKAnnotationView/MKOverlayRenderer), CoreLocation (CLLocationManagerDelegate for更新), CloudKit (CKQuerySubscription for位置/事件实时), SwiftUI (Chart for图表, Canvas for粒子动画)。
- **思路**：FootprintViewModel: @Published var tracks: [Track], var friends: [User], var propEvents: [PropEvent]。async fetchTracksFromCloudKit() -> merge本地Core Data。轨迹绘制：points to MKPolyline, renderer draw脚印Image重复 (CGContext.clip(to: path))。图表：Chart { LineMark(x: .value("Day", date), y: .value("Carbon", value)) } with Gradient.fill。道具：switch type { case .briefAnimation: withAnimation { ParticleView() }; case .persistentEffect: Timer重复GlowModifier; case .textDisplay: Overlay(Text(customData["text"] as? String)) }。位置上传：didUpdateLocations -> save to CloudKit。
- **注意事项**：性能：地图Annotation限50个，查询加predicate (timestamp > lastFetch)。电池优化：background位置用significantChange。UI响应：加载时Spinner。隐私：共享Toggle in设置。

## 阶段 2: 宠物页面开发（1.5 周）

宠物页面：显示碳宠形象、经验条、喂养按钮。UI可爱：萌感精灵、半拟物图标。

### 任务清单

1. 布局：中央宠物Image (基于level)，底部经验ProgressView + 喂养按钮，碳币消耗显示。
2. 从CloudKit查询/加载宠物数据 (fetch CarbonPet记录)。
3. 喂养逻辑：按钮tap -> 扣碳币 (update Coin记录)，加exp (update Pet记录)，动画反馈。
4. Model支持进化：虽MVP无逻辑，但if exp >= threshold { level +=1 }占位。
5. UI动画：喂养时ScaleEffect + 高光闪现。
6. 集成触觉：成功喂养UIImpactFeedbackGenerator。

### 技术实现 & 思路

- **技术栈**：SwiftUI (Image/ProgressView/withAnimation), CloudKit (fetch/save CKRecord for Pet), SceneKit optional (3D宠物 via UIViewRepresentable)。
- **思路**：PetViewModel: @Published var pet: CarbonPet?。async loadPet() -> try await CloudKitService.fetch("CarbonPet", predicate: NSPredicate(format: "userID == %@", currentUserID))。喂养：async feed() { try await CoinService.deduct(amount: pet.feedCost); pet.exp += 10; await CloudKitService.save(pet) }。Image: Image("pet_level_\(pet.level)") with .resizable().aspectRatio。动画：withAnimation(.spring()) { scale = 1.2 } then reset。
- **注意事项**：碳币不足灰化按钮。CloudKit冲突：用CKModifyRecordsOperation with savePolicy .changedKeys。离线：缓存上次pet数据。

## 阶段 3: 扫码页面开发（1 周）

扫码页面：条形码扫描 + 奖励，UI留白（大空白区域 + 提示“未来AR功能”）。

### 任务清单

1. 布局：顶部留白卡片（Text("扫描商品碳影响，未来融入AR")), 中部相机预览 + 扫描框，底部结果显示。
2. ScanService：集成相机 + 条码识别。
3. 扫描逻辑：检测条码 -> 查询CloudKit商品数据库 (Mock初始记录)，计算碳影响。
4. 奖励：成功加碳币 (update CloudKit)，显示Toast + 环保知识。
5. 判重：CloudKit查询最近扫描 (predicate: timestamp > 24h ago)。

### 技术实现 & 思路

- **技术栈**：AVFoundation (AVCaptureSession/MetadataOutput), Vision (VNDetectBarcodesRequest), CloudKit (query "ScanHistory"记录)。
- **思路**：ScanViewModel: @Published var scanResult: ScanResult?。UIViewRepresentable for相机：setMetadataDelegate捕捉VNBarcodeObservation -> barcodeValue。async processScan(code: String) { let impact = try await fetchImpactFromCloudKit(code); addCoins(5); saveScanToCloudKit() }。留白：ZStack { Color.clear; Text("AR Coming Soon").opacity(0.5) }。Toast: .sheet with Gradient card。
- **注意事项**：权限：NSCameraUsageDescription。低光：添加torch Toggle。CloudKit无数据：fallback Mock impact。

## 阶段 4: 聊天页面开发（0.5 周）

聊天页面：完全留白，作为占位。

### 任务清单

1. 布局：全屏空View + 中心Text("聊天功能开发中，敬请期待") + 可爱动画（旋转地球）。
2. 无逻辑，仅静态UI。

### 技术实现 & 思路

- **技术栈**：SwiftUI (ZStack/Text/Animation)。
- **思路**：ChatView: VStack { Spacer(); Text(...).font(.title).foregroundColor(.green); Image("earth").rotationEffect(.degrees(rotation)).animation(.linear(duration: 10).repeatForever()) ; Spacer() }。
- **注意事项**：保持轻量，无CloudKit调用。未来替换为实时聊天。

## 阶段 5: 设置页面开发（1.5 周）

设置页面：好友添加（用户名搜索）、隐私设置、账户管理。

### 任务清单

1. 布局：List { Section("好友") { 搜索栏 + 好友列表 }; Section("隐私") { Toggle("位置共享") }; Section("账户") { username显示 + 登出 } }。
2. 好友添加：TextField输入用户名 -> CloudKit查询 (predicate: "username CONTAINS %@", input)，显示结果 + 添加按钮 (save FriendRelation记录)。
3. 好友列表：从CloudKit fetch朋友，显示头像/用户名。
4. 隐私Toggle：更新CloudKit用户记录 (shareLocation: Bool)，影响位置上传。
5. 其他：版本信息、反馈按钮 (mailto)。

### 技术实现 & 思路

- **技术栈**：SwiftUI (List/Form/TextField/Toggle), CloudKit (query "User" for搜索, save "FriendRelation" as reference)。
- **思路**：SettingsViewModel: @Published var friends: [User], var searchResults: [User]。async searchUsers(query: String) { let predicate = NSPredicate(format: "username CONTAINS[c] %@", query); return try await CloudKitService.query("User", predicate: predicate) }。添加：CKReference(recordID: friendID, action: .none) to currentUser.friends array, save。Toggle绑定：onChange { await updateShareLocation($0) }。
- **注意事项**：搜索防滥：限率 (本地Timer)。CloudKit public数据库 for用户搜索，private for朋友关系。UI：圆润List行。

## 阶段 6: 全App集成 & 优化（1.5 周）

跨页面联调、性能调优。

### 任务清单

1. 串联：足迹位置 -> 设置共享；扫码奖励 -> 宠物喂养碳币；道具事件实时更新。
2. CloudKit优化：添加索引 (username/timestamp)，权限检查 (public读/user写)。
3. 异常处理：网络断 -> 本地缓存 + Alert；空态 (e.g., 无好友"添加朋友吧")。
4. 性能测试：地图/动画30min无掉帧 (Instruments CPU/GPU)。
5. 体验优化：全局动画过渡、触觉反馈、Dark Mode适配 (图像暗绿主题)。
6. Beta测试：模拟多用户，检查CloudKit同步。

### 技术实现 & 思路

- **技术栈**：Combine (Publisher for跨VM通知 e.g., coinUpdated)，Instruments (profile)。
- **思路**：AppDelegate/SceneDelegate监听CloudKit通知 (CKAccountChangedNotification)。优化查询：add sortDescriptor/timestamp filter。裁剪：地图visibleMapRect内渲染。
- **注意事项**：配额：CloudKit免费限额监控。隐私合规：GDPR式同意。最终打包：TestFlight。
